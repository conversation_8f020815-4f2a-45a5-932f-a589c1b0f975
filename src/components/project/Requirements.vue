<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useRoute } from 'vue-router';
import axios from 'axios';
import io from 'socket.io-client';
import { useAuthStore } from '../../stores/auth';
import RequirementsTable from '../project/requirements/RequirementsTable.vue';
import RequirementsUploadModal from '../project/requirements/RequirementsUploadModal.vue';
import RequirementsFilterModal from '../project/requirements/RequirementsFilterModal.vue';
import ConfirmationModal from '../common/ConfirmationModal.vue';
import { Requirement, UploadJob, JobStatusResponse, UploadJobResponse, JobStatus } from '../../types';

const route = useRoute();
const authStore = useAuthStore();
const projectId = route.params.id as string;

const getUserData = () => {
  let userId = authStore.user?.id;
  let companyId = authStore.user?.companyId;
  return { userId, companyId };
};

const { userId, companyId } = getUserData();

// State
const requirements = ref<Requirement[]>([]);
const loading = ref(false);
const error = ref('');
const searchQuery = ref('');
const selectedRequirements = ref<string[]>([]);

// Modals
const showUploadModal = ref(false);
const showFilterModal = ref(false);
const showDeleteModal = ref(false);

// Bulk delete progress tracking
const isDeletingBulk = ref(false);
const deleteProgress = ref(0);
const deleteJobId = ref<string | null>(null);

// Upload job tracking
const uploadJobs = ref<Map<string, UploadJob>>(new Map());
const socket = ref<ReturnType<typeof io> | null>(null);
const activeProgressCallbacks = ref<Map<string, (progress: number) => void>>(new Map());

// Pagination
const totalItems = ref(0);
const currentPage = ref(1);
const itemsPerPage = ref(50);

// Sorting
const sortField = ref('createdAt');
const sortDirection = ref('desc');

// Filters
const filters = ref({
  status: [] as string[],
  fileType: [] as string[],
});

const statusOptions = ['draft', 'reviewed_by_ai', 'revised', 'approved', 'published', 'rejected'];
const fileTypeOptions = ['pdf', 'md', 'txt', 'doc', 'docx'];

// Computed
const filteredRequirements = computed(() => {
  let filtered = requirements.value;

  // Apply search
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(req => 
      req.name.toLowerCase().includes(query) ||
      req.requirement_id.toLowerCase().includes(query) ||
      req.description?.toLowerCase().includes(query)
    );
  }

  // Apply filters
  if (filters.value.status.length > 0) {
    filtered = filtered.filter(req => filters.value.status.includes(req.status));
  }

  if (filters.value.fileType.length > 0) {
    filtered = filtered.filter(req => 
      req.file_type && filters.value.fileType.includes(req.file_type)
    );
  }

  return filtered;
});

const hasActiveFilters = computed(() => {
  return filters.value.status.length > 0 || 
         filters.value.fileType.length > 0 ||
         searchQuery.value.length > 0;
});

// Methods
const fetchRequirements = async () => {
  loading.value = true;
  error.value = '';
  
  try {
    const response = await axios.get(`${(import.meta as any).env.VITE_REQUIREMENT_SERVICE_URL}/api/v1/requirements`, {
      params: {
        project_id: projectId,
        page: currentPage.value,
        limit: itemsPerPage.value,
        ...filters.value
      }
    });
    
    requirements.value = response.data.data || [];
    totalItems.value = response.data.total || 0;
  } catch (err) {
    console.error('Error fetching requirements:', err);
    error.value = 'Failed to load requirements';
  } finally {
    loading.value = false;
  }
};

const handleUpload = async (file: File, onProgress?: (progress: number) => void) => {
  const formData = new FormData();
  formData.append('name', file.name);
  formData.append('project_id', projectId);
  formData.append('file', file, file.name);
  formData.append('company_id', companyId);
  formData.append('user_id', userId);

  try {
    // Start async upload and get job ID
    const response = await axios.post(`${(import.meta as any).env.VITE_REQUIREMENT_SERVICE_URL}/api/v1/requirements/upload/async`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    const uploadResponse: UploadJobResponse = response.data;
    const jobId = uploadResponse.jobId;

    if (!jobId) {
      throw new Error('No job ID returned from upload');
    }

    // Store progress callback for this job
    if (onProgress) {
      activeProgressCallbacks.value.set(jobId, onProgress);
    }

    // Track the upload job
    uploadJobs.value.set(jobId, {
      id: jobId,
      fileName: file.name,
      status: 'pending',
      progress: 0
    });

    // Subscribe to job updates via WebSocket
    if (socket.value) {
      socket.value.emit('subscribe-to-job', { jobId });
    }

    // Also start polling the job status endpoint as backup
    pollJobStatus(jobId);

    return jobId;
  } catch (err) {
    console.error('Error uploading requirement:', err);
    error.value = 'Failed to upload requirement';
    throw err;
  }
};

const handleDelete = async (requirement: Requirement) => {
  try {
    await axios.delete(`${(import.meta as any).env.VITE_REQUIREMENT_SERVICE_URL}/api/v1/requirements/${requirement.id}`);
    await fetchRequirements();
  } catch (err) {
    console.error('Error deleting requirement:', err);
    error.value = 'Failed to delete requirement';
  }
};

const handleBulkDelete = async () => {
  if (selectedRequirements.value.length === 0) return;

  console.log('Starting bulk delete...');

  try {
    isDeletingBulk.value = true;
    deleteProgress.value = 5; // Show initial progress
    console.log(`Set isDeletingBulk to: ${isDeletingBulk.value}`);

    // Start bulk delete job
    const response = await axios.delete(`${(import.meta as any).env.VITE_REQUIREMENT_SERVICE_URL}/api/v1/requirements/bulk`, {
      data: { requirementIds: selectedRequirements.value }
    });

    console.log('Bulk delete response:', response.data);
    const jobId = response.data.jobId;
    if (jobId) {
      deleteJobId.value = jobId;
      console.log(`Starting polling for job: ${jobId}`);
      // Start polling delete progress
      pollDeleteJobStatus(jobId);
    } else {
      console.log('No job ID returned, completing immediately');
      // If no job ID returned, assume immediate completion
      selectedRequirements.value = [];
      showDeleteModal.value = false;
      isDeletingBulk.value = false;
      await fetchRequirements();
    }
  } catch (err) {
    console.error('Error deleting requirements:', err);
    error.value = 'Failed to delete requirements';
    isDeletingBulk.value = false;
    deleteProgress.value = 0;
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchRequirements();
};

const handleSort = (field: string) => {
  if (sortField.value === field) {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortField.value = field;
    sortDirection.value = 'asc';
  }
  fetchRequirements();
};

const handleApplyFilters = (newFilters: typeof filters.value) => {
  filters.value = { ...newFilters };
  currentPage.value = 1;
  fetchRequirements();
};

const resetFilters = () => {
  filters.value = {
    status: [],
    fileType: [],
  };
  searchQuery.value = '';
  currentPage.value = 1;
  fetchRequirements();
};

// Poll job status using the correct endpoint
const pollJobStatus = async (jobId: string) => {
  const maxAttempts = 120; // 2 minutes with 1-second intervals
  let attempts = 0;

  const poll = async () => {
    try {
      const response = await axios.get(`${(import.meta as any).env.VITE_REQUIREMENT_SERVICE_URL}/api/v1/requirements/jobs/${jobId}/status`);
      const jobData = response.data;

      // Update job in our tracking
      const job = uploadJobs.value.get(jobId);
      if (job) {
        job.status = jobData.status;
        job.progress = jobData.progress?.percentage || 0;
        if (jobData.error) {
          job.error = jobData.error;
        }
        uploadJobs.value.set(jobId, job);

        // Call progress callback if available
        const progressCallback = activeProgressCallbacks.value.get(jobId);
        if (progressCallback) {
          console.log(`Progress update for job ${jobId}: ${job.progress}%`);
          progressCallback(job.progress);
        }
      }

      // Check if job is complete
      if (jobData.status === 'completed') {
        // Call final progress callback
        const progressCallback = activeProgressCallbacks.value.get(jobId);
        if (progressCallback) {
          progressCallback(100);
        }

        // Refresh requirements list
        await fetchRequirements();
        uploadJobs.value.delete(jobId);
        activeProgressCallbacks.value.delete(jobId);
        return;
      } else if (jobData.status === 'failed') {
        error.value = jobData.error || 'Upload failed';
        uploadJobs.value.delete(jobId);
        activeProgressCallbacks.value.delete(jobId);
        return;
      }

      // Continue polling if still processing
      attempts++;
      if (attempts < maxAttempts && (jobData.status === 'pending' || jobData.status === 'processing')) {
        setTimeout(poll, 1000); // Poll every second
      } else if (attempts >= maxAttempts) {
        error.value = 'Upload timeout - please check the queue status';
        uploadJobs.value.delete(jobId);
        activeProgressCallbacks.value.delete(jobId);
      }
    } catch (err) {
      console.error('Error polling job status:', err);
      attempts++;
      if (attempts < maxAttempts) {
        setTimeout(poll, 2000); // Retry with longer interval on error
      } else {
        error.value = 'Failed to track upload progress';
        uploadJobs.value.delete(jobId);
        activeProgressCallbacks.value.delete(jobId);
      }
    }
  };

  // Start polling
  poll();
};

// Poll delete job status using the correct endpoint
const pollDeleteJobStatus = async (jobId: string) => {
  const maxAttempts = 120; // 2 minutes with 1-second intervals
  let attempts = 0;

  const poll = async () => {
    try {
      const response = await axios.get(`${(import.meta as any).env.VITE_REQUIREMENT_SERVICE_URL}/api/v1/requirements/bulk-delete/jobs/${jobId}/status`);
      const jobData = response.data;

      // Update delete progress
      deleteProgress.value = jobData.progress?.percentage || 0;
      console.log(`Delete progress for job ${jobId}: ${deleteProgress.value}%`);
      console.log(`Modal props - isDeletingBulk: ${isDeletingBulk.value}, progress: ${Math.floor((deleteProgress.value / 100) * selectedRequirements.value.length)}, total: ${selectedRequirements.value.length}`);

      // Check if job is complete
      if (jobData.status === 'completed') {
        deleteProgress.value = 100;

        // Reset state and refresh
        selectedRequirements.value = [];
        showDeleteModal.value = false;
        isDeletingBulk.value = false;
        deleteJobId.value = null;
        await fetchRequirements();
        return;
      } else if (jobData.status === 'failed') {
        error.value = jobData.error || 'Bulk delete failed';
        isDeletingBulk.value = false;
        deleteProgress.value = 0;
        deleteJobId.value = null;
        return;
      }

      // Continue polling if still processing
      attempts++;
      if (attempts < maxAttempts && (jobData.status === 'pending' || jobData.status === 'processing')) {
        setTimeout(poll, 1000); // Poll every second
      } else if (attempts >= maxAttempts) {
        error.value = 'Delete timeout - please refresh to check status';
        isDeletingBulk.value = false;
        deleteProgress.value = 0;
        deleteJobId.value = null;
      }
    } catch (err) {
      console.error('Error polling delete job status:', err);
      attempts++;
      if (attempts < maxAttempts) {
        setTimeout(poll, 2000); // Retry with longer interval on error
      } else {
        error.value = 'Failed to track delete progress';
        isDeletingBulk.value = false;
        deleteProgress.value = 0;
        deleteJobId.value = null;
      }
    }
  };

  // Start polling
  poll();
};

const handleDeleteCancel = () => {
  if (!isDeletingBulk.value) {
    showDeleteModal.value = false;
  }
  // If delete is in progress, don't allow cancellation
};

const handleStatusUpdate = async (requirement: Requirement, action: string) => {
  try {
    console.log(`Updating requirement ${requirement.id} with action: ${action}`);

    // Map actions to API endpoints or status updates
    let newStatus = '';
    let apiEndpoint = '';

    switch (action) {
      case 'ai_analysis':
        newStatus = 'reviewed_by_ai';
        // step 1 - embedding
        // step 2 - check status of embedding job
        // step 3 - ai analysis
        // step 4 - check status of ai analysis job
        apiEndpoint = `/api/v1/requirements/${requirement.id}/ai-analysis`;
        break;
      case 'reject':
        newStatus = 'rejected';
        apiEndpoint = `/api/v1/requirements/${requirement.id}/status`;
        break;
      default:
        console.warn(`Unknown action: ${action}`);
        return;
    }

    // Make API call to update status
    await axios.patch(`${(import.meta as any).env.VITE_REQUIREMENT_SERVICE_URL}${apiEndpoint}`, {
      status: newStatus
    });

    // Refresh requirements list
    await fetchRequirements();

  } catch (err) {
    console.error('Error updating requirement status:', err);
    error.value = 'Failed to update requirement status';
  }
};

const formatStatus = (status: string) => {
  return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

// WebSocket setup
const initializeWebSocket = () => {
  socket.value = io(`${(import.meta as any).env.VITE_REQUIREMENT_SERVICE_URL}/upload-progress`, {
    transports: ['websocket'],
    autoConnect: true
  });

  socket.value.on('connect', () => {
    console.log('Connected to upload progress WebSocket');
  });

  socket.value.on('job-status-update', (data: JobStatusResponse) => {
    const job = uploadJobs.value.get(data.jobId);
    if (job) {
      job.status = data.status === JobStatus.PROCESSING ? 'processing' :
                   data.status === JobStatus.COMPLETED ? 'completed' :
                   data.status === JobStatus.FAILED ? 'failed' : 'pending';
      job.progress = data.progress.percentage;
      uploadJobs.value.set(data.jobId, job);

      // Call progress callback if available
      const progressCallback = activeProgressCallbacks.value.get(data.jobId);
      if (progressCallback) {
        progressCallback(job.progress);
      }
    }
  });

  socket.value.on('job-completed', (data: { jobId: string; result: any }) => {
    const job = uploadJobs.value.get(data.jobId);
    if (job) {
      job.status = 'completed';
      job.progress = 100;
      uploadJobs.value.set(data.jobId, job);

      // Call final progress callback
      const progressCallback = activeProgressCallbacks.value.get(data.jobId);
      if (progressCallback) {
        progressCallback(100);
      }

      // Refresh requirements list
      fetchRequirements();

      // Remove job after a delay
      setTimeout(() => {
        uploadJobs.value.delete(data.jobId);
        activeProgressCallbacks.value.delete(data.jobId);
      }, 5000);
    }
  });

  socket.value.on('job-failed', (data: { jobId: string; error: any }) => {
    const job = uploadJobs.value.get(data.jobId);
    if (job) {
      job.status = 'failed';
      job.error = data.error.message || 'Upload failed';
      uploadJobs.value.set(data.jobId, job);
    }
  });

  socket.value.on('disconnect', () => {
    console.log('Disconnected from upload progress WebSocket');
  });
};

// Lifecycle
onMounted(() => {
  fetchRequirements();
  initializeWebSocket();
});

onBeforeUnmount(() => {
  if (socket.value) {
    socket.value.disconnect();
  }
});
</script>

<template>
  <div class="requirements">
    <div class="requirements-header">
      <div class="header-left">
        <h2>Requirements</h2>
        <p class="header-subtitle">Manage project requirements and documentation with AI</p>
      </div>
      
      <div class="header-actions">
        <button
          v-if="selectedRequirements.length > 0"
          class="action-button delete-button"
          @click="showDeleteModal = true"
        >
          Delete Selected ({{ selectedRequirements.length }})
        </button>

        <button
          class="action-button upload-button"
          @click="showUploadModal = true"
        >
          Upload Requirements
        </button>
      </div>
    </div>

    <!-- Search and Filter Bar -->
    <div class="search-filter-bar">
      <div class="search-container">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Search requirements..."
          class="search-input"
          @input="fetchRequirements"
        />
      </div>
      
      <button 
        class="filter-button"
        @click="showFilterModal = true"
        :class="{ active: hasActiveFilters }"
      >
        Filter
        <span v-if="hasActiveFilters" class="filter-count">
          {{ (filters.status.length + filters.fileType.length + (searchQuery ? 1 : 0)) }}
        </span>
      </button>
    </div>

    <!-- Active Filters Display -->
    <div v-if="hasActiveFilters" class="active-filters">
      <div class="active-filters-header">
        <h4>Active Filters:</h4>
        <button @click="resetFilters" class="clear-all-button">Clear All</button>
      </div>
      
      <div class="filter-tags">
        <div v-if="searchQuery" class="filter-tag">
          Search: "{{ searchQuery }}"
        </div>
        
        <div v-for="status in filters.status" :key="`status-${status}`" class="filter-tag">
          Status: {{ formatStatus(status) }}
        </div>
        
        <div v-for="fileType in filters.fileType" :key="`fileType-${fileType}`" class="filter-tag">
          File Type: {{ fileType.toUpperCase() }}
        </div>
      </div>
    </div>

    <!-- Requirements Table -->
    <RequirementsTable
      :requirements="filteredRequirements"
      :loading="loading"
      :selected-requirements="selectedRequirements"
      :total-items="totalItems"
      :current-page="currentPage"
      :items-per-page="itemsPerPage"
      :sort-field="sortField"
      :sort-direction="sortDirection"
      @update:selected-requirements="selectedRequirements = $event"
      @delete="handleDelete"
      @page-change="handlePageChange"
      @sort="handleSort"
      @update-status="handleStatusUpdate"
    />

    <!-- Upload Modal -->
    <RequirementsUploadModal
      :show="showUploadModal"
      @close="showUploadModal = false"
      @upload="handleUpload"
    />

    <!-- Filter Modal -->
    <RequirementsFilterModal
      v-model="showFilterModal"
      :status-options="statusOptions"
      :file-type-options="fileTypeOptions"
      :initial-filters="filters"
      @apply="handleApplyFilters"
    />

    <!-- Delete Confirmation Modal -->
    <ConfirmationModal
      :is-open="showDeleteModal"
      title="Delete Requirements"
      :message="`Are you sure you want to delete ${selectedRequirements.length} requirement(s)? This action cannot be undone.`"
      :bulk-delete-in-progress="isDeletingBulk"
      :bulk-delete-progress="deleteProgress"
      :bulk-delete-total="100"
      @confirm="handleBulkDelete"
      @cancel="handleDeleteCancel"
    />
  </div>
</template>

<style lang="scss" scoped>
.requirements {
  padding: 24px;
  background-color: #f9fafb;
  min-height: 100vh;
}

.requirements-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;

  .header-left {
    h2 {
      font-size: 28px;
      font-weight: 700;
      color: #111827;
      margin: 0 0 4px 0;
    }

    .header-subtitle {
      font-size: 14px;
      color: #6b7280;
      margin: 0;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.action-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &.upload-button {
    background-color: #e94560;
    color: white;
    border: none;

    &:hover {
      background-color: #d63553;
    }
  }

  &.delete-button {
    background-color: #dc2626;
    color: white;
    border: none;

    &:hover {
      background-color: #b91c1c;
    }
  }
}

.search-filter-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;

  .search-container {
    flex: 1;
  }

  .search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;

    &:focus {
      outline: none;
      border-color: #e94560;
      box-shadow: 0 0 0 3px rgba(233, 69, 96, 0.1);
    }
  }

  .filter-button {
    padding: 8px 16px;
    background-color: white;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    position: relative;

    &:hover {
      background-color: #f9fafb;
    }

    &.active {
      background-color: #e94560;
      color: white;
      border-color: #e94560;
    }

    .filter-count {
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 10px;
      padding: 2px 6px;
      font-size: 12px;
      margin-left: 4px;
    }
  }
}

.active-filters {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 24px;

  .active-filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    h4 {
      margin: 0;
      font-size: 14px;
      color: #4b5563;
    }

    .clear-all-button {
      padding: 4px 8px;
      background-color: transparent;
      color: #e94560;
      border: 1px solid #e94560;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;

      &:hover {
        background-color: #fef2f2;
      }
    }
  }

  .filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .filter-tag {
      background-color: #e5e7eb;
      color: #374151;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
    }
  }
}
</style>
